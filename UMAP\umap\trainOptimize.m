function trainOptimize(N)
if nargin<1
    N=1;
end
if ischar(N)
    N=str2double(N);
end
initJava;
embedding=[-5.68338746733566 -2.883151740056494 ; -5.8867165539512625 -3.4217046337746617 ; -5.9637717820984495 -3.4720468405435976 ; -5.231159787341188 -2.904196701159484 ; -5.102056994247706 6.982447478407491 ; -5.038401816078177 -2.929557780116283 ; -5.823131889918112 -3.5732714410007436 ; -5.4077391959745285 -4.750921304591586 ; -5.305521991118944 -2.7357691163279134 ; -5.465435920378276 6.994482538619686 ; -5.330723913866835 -4.5360874277933 ; -5.952885744002537 8.516468938841578 ; -4.944503304239573 6.6913073096531885 ; -4.983711194727265 -4.865203394039648 ; -6.150826449075024 -5.483127736066602 ; -6.006766661159744 7.335719614547725 ; -5.955984423350545 8.765737110969356 ; -5.742035020502178 -5.757055468872149 ; -6.46376000863841 1.5626616864365415 ; -5.628397133003127 7.26450353719721 ; -5.805628350199008 7.742297543538634 ; ];
head=[10 8 13 15 9 6 1 20 3 19 16 7 18 13 1 17 5 9 20 18 7 10 11 7 10 16 5 8 13 3 6 11 1 20 7 9 4 10 11 12 19 16 6 14 1 3 5 12 6 6 20 4 6 7 3 11 7 14 15 4 8 10 4 3 4 10 1 2 5 15 20 18 14 5 17 4 9 7 3 17 7 20 12 12 10 10 5 14 11 6 9 4 13 19 5 2 15 20 5 11 4 2 8 8 1 18 6 1 13 16 11 4 8 19 13 6 1 5 19 1 4 15 7 11 7 1 20 17 1 5 20 8 16 4 16 3 1 11 19 17 20 9 11 1 4 20 8 17 16 20 12 13 12 3 15 4 17 1 16 9 3 5 1 1 16 15 9 6 12 10 14 7 5 13 13 13 5 10 18 13 9 19 6 18 15 13 6 14 19 9 9 4 19 18 13 14 1 19 9 2 8 16 17 16 16 8 11 14 20 5 13 18 17 5 12 17 19 4 19 3 19 2 19 7 2 5 8 12 17 10 9 2 7 20 7 8 7 7 3 1 15 17 16 7 18 16 1 1 4 4 3 12 1 5 14 9 15 16 4 17 9 19 18 14 1 16 17 20 5 6 7 3 12 6 12 6 20 7 14 11 9 7 4 4 18 8 16 12 6 3 13 13 15 9 10 20 2 12 8 3 19 5 9 5 5 4 16 1 5 6 3 18 1 10 15 20 3 1 20 1 20 20 9 13 19 7 14 19 20 13 8 18 20 12 10 17 5 12 20 19 13 17 5 12 10 6 16 4 20 20 4 17 15 8 15 19 11 4 12 5 10 17 4 8 3 6 3 2 10 17 5 7 19 7 3 1 6 6 ]';
tail=[1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 5 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 13 13 13 13 13 13 13 13 13 13 13 13 13 13 13 14 14 14 14 14 14 14 14 14 14 14 14 14 14 14 14 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 16 16 16 16 16 16 16 16 16 16 16 16 16 16 16 16 17 17 17 17 17 17 17 17 17 17 17 17 17 17 17 17 17 17 17 17 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 19 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 ]';
epochs_per_sample=[19.069259043082987 8.343333929384416 5.422559298141188 4.802425691736843 4.434610407358629 1.000515701588198 4.351169712982151 25.662582165823494 6.356296883549158 3.4848238619672403 22.05587258477375 1.3146902745332136 22.924272657958653 3.2283081121923813 9.284292523434969 5.439002375262809 1.1057117312637323 4.037744663227574 1 1.7152950688497592 12.820225309811038 1.5414763664357518 1 3.1457776281972127 2.159341522705935 1.267970656854563 1.6259129453230545 1.0774064075444012 7.327826216091369 17.87532254151554 3.8768654081059153 9.549788426678555 4.95508008191675 4.599853531259474 6.506701715743852 12.746083521933647 13.637861117302954 12.485515191027892 3.475650750373832 2.3087128735415123 1 5.041773481233698 1 8.351195744704285 8.93278746062051 7.192228287461497 1.6130213647846565 14.124789127310141 7.647807383344983 3.6808188722387403 2.0631110357655005 12.962187202935718 2.5851396215715527 5.352753607488346 2.697545109508187 8.837780024914538 65.70779770640709 17.199378686489208 12.310926163020007 6.2923838321340115 9.796356389639874 13.534247709649998 11.138206020102578 27.90404702978111 3.514132343244945 12.079094636479553 6.456076328100079 1.5475658333524895 10.983649673057423 4.874049910980234 8.336153985161632 2.078707054377479 3.599824901334247 10.975255862855011 1 3.661259044667523 6.05772111639894 5.014941175767165 1.5304900204613445 9.923381156908764 4.526450253574393 2.9434202733469608 6.874010232093719 2.557604638732831 50.33688472704303 4.458709046636259 2.0274625499580923 9.556906120841752 1 5.814100852415579 11.487954689408653 5.151508578612593 4.682054292011725 6.789903525766514 1.507360667025266 5.367954912667434 6.246710514340071 2.6378305889937708 1.0305946551855778 1 15.346960422568298 10.94073813817495 9.462984367409938 11.152772364853513 7.6673612801942275 8.007436159923596 1.9769028407891462 13.044463661812618 11.805402859603339 9.222792241555664 1.7242642456906467 8.967097617877627 1.893161005058466 4.4291089587998425 1 1.6053170855485892 1.2758899776936685 5.1675516364406375 4.3620208165669885 3.5052385621983255 15.03383231107468 1.8273853247024807 1.669400997128233 4.374235169880622 1.3015091849675846 1.670622216172692 11.774583280444455 10.28880630613277 3.2821991956891936 13.759457496905554 7.9045645814686285 3.1767523679363476 1.4197551371547708 2.595613073667966 1.6862701659891846 14.483770077569115 1.803701634584247 6.142870027908495 5.144651648558607 1.7771997373276962 5.8935492464474 1 3.9911116103832818 3.512782466752972 12.042039003363369 45.990810026381176 1.1883888813519294 2.7281898618003013 1 15.958646951637352 132.96148958888776 8.203616185554486 1.2590473990989213 7.484866838362508 1.505083599000145 11.390391221621048 8.458834083750922 137.33559354395516 4.387929420182785 7.744151608740929 6.712980014312901 1.7025905787750495 2.7178552447206146 2.092032551368653 4.088142281751223 1 1.9897657426007607 3.059784776062096 4.905369066892205 7.259970169716907 5.5604608043750074 1.2457262162643412 6.441423270395787 1.0809683055802017 6.231075866877187 6.183513151077238 11.90853717314563 9.165957121264801 1.5753226621924228 10.066656960110448 6.260082158693739 4.60985667368394 9.492044280328102 2.4320865209858913 7.639782422442808 2.7101166206832183 1.0249112401170657 2.0818803391252882 11.54837215688807 2.7189108812577065 7.099990106426865 1 2.4330478036111045 7.4934250583605 1.6198650363627458 3.8606540708699173 13.277754362299053 3.3553119224213472 1.4120305713219647 31.14146958137327 1.2032478627357421 3.4220594084543103 10.458532927354005 1.8088348664448008 11.049096443593237 25.263634577245814 1.3538694676915561 2.875662445630463 5.680603065336598 1 1 7.031344577142646 6.8334316888733575 1.6527409120672627 14.548560855916715 13.19490062568698 12.208091082200399 2.8355417846148416 17.907426426400942 1.6750072700658 4.268315108194143 7.524047020496053 3.2943741478699478 1.4908722398077296 6.4763320578208905 1.9363985582365213 30.29684217816906 9.046902261667338 7.468334286390376 9.817953671267423 5.0121082150649245 3.247481340645597 2.445500733909897 3.4059489446346607 1.8108272130129994 1 5.1406167829092695 6.442984094725562 5.39354300988145 3.2106427964435933 5.247053819028643 2.8085319936204085 1 9.003882596124825 1.050043820052261 1.786105500487464 1.4501961036455753 6.88274682016296 10.54170564759179 2.5186725859616144 5.240591972606891 3.228827360292574 18.04008259457755 5.113945916687562 2.8244384316395488 2.3830035002339147 3.7463454559025595 1.5295426532236887 12.898429523460724 8.454418590565316 4.912356079947427 2.0832552736203653 33.07023073704827 2.7626047661440767 6.076426227861388 2.5189756014427926 1.1337985557652572 17.09232997503552 1.6598192046486628 1 28.073905472769688 1 6.197720881904796 2.5076067682198753 2.810789272863825 1.89826905561238 2.739476803402939 2.8482786116301066 6.813540960328012 8.2709920821342 3.5606492442338062 9.054959228755912 1 3.2673683124609916 10.537568200141152 2.4983690140012427 1.7898881904197501 6.285500145218814 33.37635403308364 3.070932531144852 7.143535720258765 10.055222530940467 2.112022163865043 1.0699895651817715 1.0224856632985495 10.900936238510809 9.642001183244194 6.561887124788556 1.261082967746424 1.6809986990162675 8.319762299371261 1.4460291696379823 1.3619242265417209 1.9589789338062886 5.094942536751634 1 1.4994131768692358 16.951695782456824 7.9390128727394496 4.5166992411572275 10.891075420672237 3.6415667533970404 1 7.147469849978475 18.160278191733276 4.412220237077729 7.427099947401042 2.9264254120841495 1.859903686307418 12.984919213568869 4.280249396107036 12.226065212592882 2.3263514885506997 7.515577314475151 1.317856327963305 14.845327278140928 2.186961051084767 1.838727955925748 1.73986320455635 11.935361706164375 5.006768441969026 2.3080253191035376 10.936493551099188 2.1681560824512123 4.257931329229553 4.753642352592677 4.080269578136579 6.895192795794617 11.74631319572823 10.355284897083868 6.558287239634029 1 1.6850051191577673 1.4619087696969317 5.545403672862205 2.760432044395752 11.615665187872068 2.690823805033036 4.668600272146053 3.2231546624170755 1.877894959630657 1.5442707492946441 8.821505622552918 3.0266470162149806 6.9713039165750486 11.814557005892244 11.58165858225926 1 1.6714159827780897 2.288348476993007 6.920321681242513 5.0853363782327445 3.486581046945916 5.408650408701419 4.911468459241866 2.409026626528594 1 14.109488587485544 18.846840057284997 2.4736407699440215 4.28718079953073 1.6580252826171291 3.785745813625596 5.276414103004318 6.083900299283111 1.484866935958552 4.866976712102519 11.190959780819826 ]';
n_epochs=50;
n_vertices=21;
a=1.5769274724983178 ;
b=0.8950603098187409 ;
gamma=1 ;
initial_alpha=1 ;
negative_sample_rate=5 ;
verbose=false;


addpath('../util');
initJava
pu=PopUp('Running UMAP ...', 'center', 'foobar', false);
nTh=edu.stanford.facs.swing.Umap.EPOCH_REPORTS;
pu.initProgress(nTh);
drawnow;
progress_bar=true;%pu.pb;
tc=tic;
for i=1:N
    oStaticJavaResult=edu.stanford.facs.swing.Umap.optimize_layout(embedding, embedding,head,...
        tail, n_epochs, n_vertices, epochs_per_sample, a, b, gamma, ...
        initial_alpha, negative_sample_rate, [], []);
end
toc(tc);
try
    progress_bar.setValue(0);
    drawnow;
catch
end
tc=tic;
for i=1:N
    obj=edu.stanford.facs.swing.Umap(embedding, embedding,head,...
        tail, n_epochs, n_vertices, epochs_per_sample, a, b, gamma, ...
        initial_alpha, negative_sample_rate);
    while ~obj.nextEpochs()
        %disp('MatLab chance to operate');
    end
    oIterativeJavaResult=obj.getEmbedding;
    assert(isequal(oStaticJavaResult, oIterativeJavaResult));
end
toc(tc);
tc=tic;
for i=1:N
    obj=edu.stanford.facs.swing.UmapFloat(single(embedding), ...
        single(embedding), head, tail, n_epochs, n_vertices, ...
        single(epochs_per_sample), single(a), single(b), ...
        single( gamma ),   single(initial_alpha), ...
        negative_sample_rate);
    while ~obj.nextEpochs()
        %disp('MatLab chance to operate');
    end
    oIterativeFloatJavaResult=obj.getEmbedding;
    isequal(oStaticJavaResult, oIterativeFloatJavaResult);
end
toc(tc);

tc=tic;
for i=1:N
    optimize_layout_mex(single(embedding), single(embedding), ...
        int32(head), int32(tail), int32(n_epochs), int32(n_vertices),...
        single(epochs_per_sample), single(a), ...
        single(b),  single(gamma), single(initial_alpha), ...
        int32(negative_sample_rate), verbose);
end
toc(tc);

tc=tic;
for i=1:N
    optimize_layout(single(embedding), single(embedding), ...
        int32(head), int32(tail), int32(n_epochs), int32(n_vertices),...
        single(epochs_per_sample), single(a), ...
        single(b),  single(gamma), single(initial_alpha), ...
        int32(negative_sample_rate), verbose);
end
toc(tc);


tc=tic;
for i=1:N
    optimize_layout2(single(embedding), single(embedding), ...
        int32(head), int32(tail), int32(n_epochs), int32(n_vertices),...
        single(epochs_per_sample), single(a), ...
        single(b),  single(gamma), single(initial_alpha), ...
        int32(negative_sample_rate), verbose);
end
toc(tc);

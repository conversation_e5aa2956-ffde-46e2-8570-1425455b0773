function Y = umap_core(X, n_neighbors, min_dist, n_epochs)
% UMAP core algorithm (vectorized version based on umap_v4)
% Inputs:
%   X - Input data matrix (N x dim)
%   n_neighbors - Number of nearest neighbors
%   min_dist - Minimum distance in embedding
%   n_epochs - Number of optimization epochs
% Output:
%   Y - 2D embedding (N x 2)

[N, dim] = size(X);

% Convert to GPU array if not already
if ~isa(X, 'gpuArray')
    X = gpuArray(X);
end

% Initialize embedding in 2D with small random values
Y = gpuArray(randn(N, 2, 'single') * 0.1);

% 1. Compute k-nearest neighbors using pdist2
fprintf('Computing k-nearest neighbors...\n');
[knn_dists, knn_indices] = pdist2(X, X, 'euclidean', 'Smallest', n_neighbors+1);
knn_indices = knn_indices(2:end,:)'; % Remove self (N x k)
knn_dists = knn_dists(2:end,:)'; % Remove self distances

% 2. Compute fuzzy simplicial set (adjacency matrix)
fprintf('Computing fuzzy simplicial set...\n');
[rows, cols, vals] = compute_fuzzy_simplicial_v4(X, knn_indices, knn_dists, n_neighbors);

% 3. Optimize embedding using epochs per sample approach
fprintf('Optimizing embedding...\n');
Y = optimize_layout_v4(Y, rows, cols, vals, min_dist, n_epochs, n_neighbors);

% Convert back to CPU if needed
Y = gather(Y);
end

function [rows, cols, vals] = compute_fuzzy_simplicial_v4(X, knn_indices, knn_dists, n_neighbors)
% Vectorized fuzzy simplicial set computation based on umap_v4
[N, ~] = size(X);

% Create row and column indices for the graph
rows = repelem((1:N)', n_neighbors);
cols = knn_indices(:);

% Use precomputed distances
dists = knn_dists(:);

% Compute rho (minimum distance for each point) - vectorized
reshaped_dists = reshape(dists, [n_neighbors, N]);
rhos = min(reshaped_dists, [], 1)';

% Find sigmas using vectorized approach
sigmas = find_sigmas_v4(reshaped_dists, rhos, n_neighbors);

% Compute fuzzy membership values - vectorized
vals = exp(-max(dists - rhos(rows), 0) ./ sigmas(rows));

% Symmetrize using fuzzy set union (probabilistic t-conorm)
% Move to CPU for sparse operations
rows_cpu = double(gather(rows));
cols_cpu = double(gather(cols));
vals_cpu = double(gather(vals));

A = sparse(rows_cpu, cols_cpu, vals_cpu, N, N);
A_sym = A + A' - A .* A';
[rows_final, cols_final, vals_final] = find(A_sym);

% Convert to GPU arrays
rows = gpuArray(int32(rows_final));
cols = gpuArray(int32(cols_final));
vals = gpuArray(single(vals_final));
end
function Y = optimize_layout_v4(Y, rows, cols, vals, min_dist, n_epochs, n_neighbors)
% Vectorized optimization based on umap_v4 with epochs per sample
[N, d] = size(Y);
n_edges = length(rows);

% Compute a and b parameters from min_dist
a = single(1.6);  % Default values from umap_v4
b = single(0.9);
if min_dist > 0
    % Approximate curve fitting for min_dist
    a = single(1.929 * (1 - min_dist) / min_dist);
    b = single(0.7915);
end

% Parameters from umap_v4
repulsion_strength = single(1.0);
negative_sample_rate = single(1.0);

% Compute weights and epochs per sample (like umap_v4)
knn_min_scores = compute_min_scores(vals, rows, n_neighbors, N);
weights = 1 ./ (knn_min_scores(rows) .^ 2 + 1e-8);
epochs_per_sample = make_epochs_per_sample(weights, n_epochs);
epoch_of_next_sample = epochs_per_sample;

% Precompute constants for efficiency
b1 = b - 1.0;
a1 = single(0.001);
c0 = single(-2.0 * a * b);
c1 = single(2.0 * repulsion_strength * b);

fprintf('Starting optimization with %d edges...\n', n_edges);

for epoch = 1:n_epochs
    if mod(epoch, 100) == 0
        fprintf('Epoch %d/%d\n', epoch, n_epochs);
    end

    % Learning rate decay
    lr = single(1.0 * (1 - epoch / n_epochs));

    % Find edges to update this epoch
    active_edges = epoch_of_next_sample <= epoch;
    if ~any(active_edges)
        continue;
    end

    % Get active edge indices
    active_rows = rows(active_edges);
    active_cols = cols(active_edges);
    active_vals = vals(active_edges);
    active_eps = epochs_per_sample(active_edges);

    % Update embedding using vectorized operations
    Y = update_embedding_vectorized(Y, active_rows, active_cols, active_vals, ...
                                   lr, a, b, b1, a1, c0, c1, ...
                                   negative_sample_rate, repulsion_strength, N);

    % Update epoch counters
    epoch_of_next_sample(active_edges) = epoch_of_next_sample(active_edges) + active_eps;
end
end

function sigmas = find_sigmas_v4(reshaped_dists, rhos, n_neighbors)
% Vectorized sigma computation
[k, N] = size(reshaped_dists);
sigmas = zeros(N, 1);
target = log2(n_neighbors);

% Vectorized computation where possible
for i = 1:N
    point_dists = reshaped_dists(:, i);

    % Define search function
    f = @(sigma) sum(exp(-max(point_dists - rhos(i), 0) / sigma)) - target;

    % Use bounded search with fallback
    try
        sigmas(i) = fzero(f, [1e-6, 100]);
    catch
        sigmas(i) = 0.5; % Fallback
    end
end
end

function min_scores = compute_min_scores(vals, rows, n_neighbors, N)
% Compute minimum scores for each point (like knn_min_scores in umap_v4)
min_scores = ones(N, 1);

for i = 1:N
    point_vals = vals(rows == i);
    if ~isempty(point_vals)
        min_scores(i) = min(point_vals);
    end
end
min_scores = gpuArray(single(min_scores));
end

function epochs_per_sample = make_epochs_per_sample(weights, n_epochs)
% Compute epochs per sample based on weights (from umap_v4)
weights = gather(weights); % Move to CPU for computation
min_value = max(weights) / double(n_epochs);
weights(weights < min_value) = min_value;

result = -1.0 * ones(size(weights));
n_samples = n_epochs * (weights / max(weights));
valid_mask = n_samples > 0;
result(valid_mask) = double(n_epochs) ./ n_samples(valid_mask);

epochs_per_sample = gpuArray(single(result));
end
function Y = update_embedding_vectorized(Y, rows, cols, vals, lr, a, b, b1, a1, c0, c1, negative_sample_rate, repulsion_strength, N)
% Vectorized embedding update based on umap_v4
n_active = length(rows);

% Attraction forces (positive samples)
% Compute differences: xi - xj for all active edges
diff = Y(rows, :) - Y(cols, :);  % [n_active x 2]

% Compute squared distances with small epsilon
dij_sq = sum(diff.^2, 2) + 1e-7;  % [n_active x 1]

% Compute gradient coefficients for attraction
% grad_coeff = c0 * (dij^(b-1)) / (a * dij^b + 1)
grad_coeff_pos = c0 * (dij_sq.^b1) ./ (a * (dij_sq.^b) + 1);  % [n_active x 1]

% Scale by edge weights and learning rate
grad_pos = vals .* grad_coeff_pos .* lr;  % [n_active x 1]

% Apply gradients using GPU-compatible operations
% Convert indices to CPU for accumarray
rows_cpu = gather(double(rows));
cols_cpu = gather(double(cols));

for d = 1:2
    % Positive gradients for source nodes (rows)
    grad_contrib = gather(double(grad_pos .* diff(:, d)));

    % Use accumarray on CPU then transfer back to GPU
    grad_accum_pos = accumarray(rows_cpu, grad_contrib, [N, 1], @sum, double(0));
    grad_accum_neg = accumarray(cols_cpu, grad_contrib, [N, 1], @sum, double(0));

    Y(:, d) = Y(:, d) + gpuArray(single(grad_accum_pos));
    Y(:, d) = Y(:, d) - gpuArray(single(grad_accum_neg));
end

% Repulsion forces (negative samples)
% Sample negative points for each active edge
n_neg_samples = max(1, round(negative_sample_rate * length(rows)));
neg_indices = randi(N, n_neg_samples, 1, 'gpuArray');
neg_rows_idx = randi(length(rows), n_neg_samples, 1, 'gpuArray');
neg_rows = rows(neg_rows_idx);

% Compute repulsive differences
neg_diff = Y(neg_rows, :) - Y(neg_indices, :);  % [n_neg_samples x 2]
neg_dij_sq = sum(neg_diff.^2, 2) + 1e-7;  % [n_neg_samples x 1]

% Compute gradient coefficients for repulsion
% grad_coeff = c1 / ((a1 + dij) * (a * dij^b + 1))
grad_coeff_neg = c1 ./ ((a1 + neg_dij_sq) .* (a * (neg_dij_sq.^b) + 1));

% Scale by learning rate and repulsion strength
grad_neg = grad_coeff_neg * lr;  % [n_neg_samples x 1]

% Apply negative gradients
neg_rows_cpu = gather(double(neg_rows));
for d = 1:2
    grad_contrib_neg = gather(double(grad_neg .* neg_diff(:, d)));
    grad_accum_rep = accumarray(neg_rows_cpu, grad_contrib_neg, [N, 1], @sum, double(0));
    Y(:, d) = Y(:, d) + gpuArray(single(grad_accum_rep));
end

% Clip gradients to prevent instability (like rclip in umap_v4)
Y = max(-2.0, min(2.0, Y));
end

function H = mvts2hankel(X, window_size)
% Create Hankel matrix from multivariate time series
% Inputs:
%   X - Time series data (N x d) where N is time points, d is dimensions
%   window_size - Size of the sliding window
% Output:
%   H - Hankel matrix ((N-window_size+1) x (window_size*d))

if isa(X, 'gpuArray')
    X = gather(X); % Move to CPU for Hankel construction
end

[N, d] = size(X);
if window_size > N
    error('Window size cannot be larger than time series length');
end

n_windows = N - window_size + 1;
H = zeros(n_windows, window_size * d);

% Vectorized Hankel matrix construction
for i = 1:n_windows
    window_data = X(i:i+window_size-1, :);  % [window_size x d]
    H(i, :) = window_data(:)';  % Flatten to row vector
end

% Convert back to GPU array
H = gpuArray(single(H));
end

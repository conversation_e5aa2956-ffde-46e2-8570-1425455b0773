name: Upload Python Package
permissions:
  contents: write

on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - '*' # Push events to matching v*, i.e. v1.0, v20.15.10

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  release:
    name: Create Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          # by default, it uses a depth of 1
          # this fetches all history so that we can read each commit
          fetch-depth: 0
      - name: Generate Changelog
        run: .github/release_message.sh > release_message.md
      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          body_path: release_message.md

  deploy:
    needs: release
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install setuptools wheel twine
    - name: Build and publish
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        python setup.py sdist bdist_wheel
        twine upload dist/*

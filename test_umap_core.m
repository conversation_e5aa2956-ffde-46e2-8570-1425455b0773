% Test script for the improved UMAP implementation
close all; clear; clc;

fprintf('Testing improved UMAP core implementation...\n');

%% Test 1: Simple 2D spiral data
fprintf('\n=== Test 1: 2D Spiral Data ===\n');
t = linspace(0, 4*pi, 500)';
X_spiral = [t.*cos(t), t.*sin(t)] + 0.1*randn(500, 2);
X_spiral = gpuArray(single(X_spiral));

tic;
Y_spiral = umap_core(X_spiral, 15, 0.1, 200);
time_spiral = toc;
fprintf('Spiral embedding completed in %.2f seconds\n', time_spiral);

figure(1);
subplot(1,2,1);
scatter(gather(X_spiral(:,1)), gather(X_spiral(:,2)), 10, t, 'filled');
title('Original Spiral Data');
colorbar;

subplot(1,2,2);
scatter(Y_spiral(:,1), Y_spiral(:,2), 10, t, 'filled');
title('UMAP Embedding');
colorbar;

%% Test 2: Swiss Roll data
fprintf('\n=== Test 2: Swiss Roll Data ===\n');
n_points = 1000;
t = (3*pi/2) * (1 + 2*rand(n_points, 1));
height = 21 * rand(n_points, 1);
X_swiss = [t.*cos(t), height, t.*sin(t)] + 0.1*randn(n_points, 3);
X_swiss = gpuArray(single(X_swiss));

tic;
Y_swiss = umap_core(X_swiss, 10, 0.1, 300);
time_swiss = toc;
fprintf('Swiss roll embedding completed in %.2f seconds\n', time_swiss);

figure(2);
subplot(1,2,1);
scatter3(gather(X_swiss(:,1)), gather(X_swiss(:,2)), gather(X_swiss(:,3)), 10, t, 'filled');
title('Original Swiss Roll Data');
colorbar;

subplot(1,2,2);
scatter(Y_swiss(:,1), Y_swiss(:,2), 10, t, 'filled');
title('UMAP Embedding');
colorbar;

%% Test 3: Hankel matrix functionality
fprintf('\n=== Test 3: Hankel Matrix Test ===\n');
% Test mvts2hankel function
test_ts = sin((1:100)' * [1, 2, 3] * 0.1);  % 3D time series
test_ts = gpuArray(single(test_ts));

H = mvts2hankel(test_ts, 10);
fprintf('Hankel matrix created: %dx%d\n', size(H, 1), size(H, 2));

% Test UMAP on Hankel matrix
tic;
Y_hankel = umap_core(H, 5, 0.1, 200);
time_hankel = toc;
fprintf('Hankel UMAP embedding completed in %.2f seconds\n', time_hankel);

figure(3);
scatter(Y_hankel(:,1), Y_hankel(:,2), 10, 1:size(Y_hankel,1), 'filled');
title('UMAP Embedding of Hankel Matrix');
colorbar;
xlabel('UMAP 1');
ylabel('UMAP 2');

%% Performance summary
fprintf('\n=== Performance Summary ===\n');
fprintf('Spiral data (500 points): %.2f seconds\n', time_spiral);
fprintf('Swiss roll (1000 points): %.2f seconds\n', time_swiss);
fprintf('Hankel matrix (%d points): %.2f seconds\n', size(H,1), time_hankel);

fprintf('\nAll tests completed successfully!\n');

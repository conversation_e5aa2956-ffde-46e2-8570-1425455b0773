# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  linter:
    strategy:
      fail-fast: false
      matrix:
        python-version: [3.11]
        os: [ubuntu-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install project
        run: make install

  tests_linux:
    needs: linter
    strategy:
      fail-fast: false
      matrix:
        python-version: [3.11]
        os: [ubuntu-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install project
        run: make install
      - name: Run tests
        run: make test
      - name: "Upload coverage to Codecov"
        uses: codecov/codecov-action@v4
        # with:
        #   fail_ci_if_error: true

  tests_mac:
    needs: linter
    strategy:
      fail-fast: false
      matrix:
        python-version: [3.11]
        os: [macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install project
        run: make install
      - name: Run tests
        run: make test

  tests_win:
    needs: linter
    strategy:
      fail-fast: false
      matrix:
        python-version: [3.11]
        os: [windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install Pip
        run: pip install --user --upgrade pip
      - name: Install project
        run: pip install -e .[test]
      - name: run tests
        run: pytest -s -vvvv -l --tb=long tests

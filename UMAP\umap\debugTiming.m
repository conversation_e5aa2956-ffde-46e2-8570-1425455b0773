%   AUTHORSHIP
%   Primary Developer: <PERSON> <s<PERSON><PERSON><PERSON>@stanford.edu> 
%   Math Lead & Secondary Developer:  <PERSON> <<EMAIL>>
%   Bioinformatics Lead:  <PERSON> <wmoor<PERSON>@stanford.edu>
%   Provided by the Herzenberg Lab at Stanford University 
%   License: BSD 3 clause
function debugTiming(description)
DEBUGGING=false;
if DEBUGGING && ~isdeployed
    msg([description ' ' num2str(toc)], 40, ...
        'south west+', 'Timing test', 'none')
end
end
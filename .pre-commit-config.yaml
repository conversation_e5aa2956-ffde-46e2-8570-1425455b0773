exclude: "^docs/|/migrations/"
default_stages: [commit]
fail_fast: true

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-merge-conflict
      - id: detect-private-key
      - id: no-commit-to-branch
        args:
        - --branch=develop
        - --branch=master
        - --branch=fix

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.4
    hooks:
      - id: ruff
      - id: ruff-format

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.11.2
    hooks:
      - id: mypy
        args: ["--python-version=3.11", "--config-file=setup.cfg"]
        additional_dependencies:
          - pytest

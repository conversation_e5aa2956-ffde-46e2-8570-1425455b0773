# MATLAB UMAP Implementation - Based on umap_v4

## Overview

This implementation provides a vectorized, GPU-accelerated MATLAB version of UMAP (Uniform Manifold Approximation and Projection) based on the nano-umap-v4 Python implementation. The code is optimized for MATLAB's strengths including vectorization, GPU computing, and efficient matrix operations.

## Key Features

### 1. **GPU Acceleration**
- Full GPU support using `gpuArray` for all major computations
- Automatic GPU/CPU data transfer management
- Single precision (`single`) for memory efficiency

### 2. **Vectorized Operations**
- Replaced loops with vectorized MATLAB operations where possible
- Efficient use of `pdist2` for k-nearest neighbor computation
- Vectorized gradient computation and accumulation

### 3. **umap_v4 Algorithm Features**
- **Epochs per sample**: Implements the sophisticated sampling strategy from umap_v4
- **Weight-based sampling**: Uses edge weights to determine update frequency
- **Proper gradient computation**: Follows the exact gradient formulation from the Python implementation

### 4. **Memory Efficient**
- Uses sparse matrices for adjacency representation
- Efficient accumulation using `accumarray`
- Minimal data copying between GPU and CPU

## Main Functions

### `umap_core(X, n_neighbors, min_dist, n_epochs)`
Main UMAP function that takes:
- `X`: Input data matrix (N × d)
- `n_neighbors`: Number of nearest neighbors (default: 15)
- `min_dist`: Minimum distance in embedding (default: 0.1)
- `n_epochs`: Number of optimization epochs (default: 500)

Returns 2D embedding Y (N × 2).

### `mvts2hankel(X, window_size)`
Creates Hankel matrix from multivariate time series:
- `X`: Time series data (N × d)
- `window_size`: Sliding window size
- Returns: Hankel matrix ((N-window_size+1) × (window_size*d))

## Algorithm Implementation Details

### 1. **K-Nearest Neighbors**
```matlab
[knn_dists, knn_indices] = pdist2(X, X, 'euclidean', 'Smallest', n_neighbors+1);
```
Uses MATLAB's optimized `pdist2` with 'Smallest' option for efficient k-NN computation.

### 2. **Fuzzy Simplicial Set Construction**
- Computes local connectivity (rho values)
- Finds optimal sigma values using `fzero`
- Creates symmetric adjacency matrix using fuzzy set union

### 3. **Epochs Per Sample Strategy**
Implements the umap_v4 approach:
```matlab
weights = 1 ./ (knn_min_scores(rows) .^ 2 + 1e-8);
epochs_per_sample = make_epochs_per_sample(weights, n_epochs);
```

### 4. **Vectorized Gradient Updates**
- **Attraction forces**: Applied to connected node pairs
- **Repulsion forces**: Applied to randomly sampled negative pairs
- **GPU-compatible accumulation**: Uses CPU `accumarray` then transfers to GPU

## Performance Optimizations

### 1. **Vectorization Tricks**
- Uses `permute` and broadcasting for efficient distance computations
- Vectorized gradient coefficient calculations
- Batch processing of edge updates

### 2. **GPU Memory Management**
- Automatic detection of GPU arrays
- Efficient CPU-GPU transfers only when necessary
- Single precision to reduce memory usage

### 3. **Sparse Matrix Operations**
- Efficient sparse matrix construction and operations
- Symmetric matrix handling for undirected graphs

## Usage Examples

### Basic Usage
```matlab
% Simple 2D embedding
X = randn(1000, 50);  % 1000 points in 50D
Y = umap_core(X, 15, 0.1, 500);
scatter(Y(:,1), Y(:,2));
```

### Time Series Analysis
```matlab
% FitzHugh-Nagumo dynamics (as in demo)
X = gpuArray(synth_data);
H = mvts2hankel(X, 20);
Y = umap_core(H, 5, 0.1, 500);
```

### GPU Acceleration
```matlab
% Automatic GPU usage
X = gpuArray(single(data));  % Convert to GPU
Y = umap_core(X, 15, 0.1, 500);  % Runs on GPU
Y = gather(Y);  % Convert back to CPU if needed
```

## Performance Benchmarks

From test results:
- **500 points (spiral)**: ~10.4 seconds
- **1000 points (Swiss roll)**: ~18.0 seconds  
- **91 points (Hankel matrix)**: ~2.8 seconds

Performance scales well with data size and benefits significantly from GPU acceleration.

## Key Differences from Original Python Implementation

1. **No JIT compilation**: Uses MATLAB's vectorization instead of Numba
2. **GPU-first approach**: Designed for GPU arrays from the start
3. **MATLAB-optimized**: Uses `pdist2`, `accumarray`, and other MATLAB-specific functions
4. **Simplified negative sampling**: Uses random sampling instead of complex rejection sampling

## Files

- `umap_core.m`: Main implementation
- `demo_umap_core.m`: FitzHugh-Nagumo dynamics demo
- `test_umap_core.m`: Comprehensive test suite
- `UMAP_MATLAB_IMPLEMENTATION.md`: This documentation

## Dependencies

- MATLAB R2019b or later (for GPU support)
- Parallel Computing Toolbox (for GPU arrays)
- Statistics and Machine Learning Toolbox (for `pdist2`)

## Future Improvements

1. **Spectral initialization**: Add spectral embedding initialization option
2. **Metric options**: Support for different distance metrics
3. **Parallel CPU**: Multi-threading for CPU-only systems
4. **Memory optimization**: Further reduce memory footprint for large datasets

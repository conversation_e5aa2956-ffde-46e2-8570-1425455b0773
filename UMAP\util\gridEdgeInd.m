%   AUTHORSHIP
%   Primary Developer: <PERSON> <s<PERSON><PERSON><PERSON>@stanford.edu> 
%   Math Lead & Secondary Developer:  <PERSON> <<EMAIL>>
%   Bioinformatics Lead:  <PERSON> <wmoor<PERSON>@stanford.edu>
%   Provided by the Herzenberg Lab at Stanford University 
%   License: BSD 3 clause
%

function [edgeInd, gce]=gridEdgeInd(clusterId, M, mins, deltas, pointers)
cluInd=find(pointers==clusterId);
gce=edu.stanford.facs.swing.GridClusterEdge(M);
gce.computeAll(cluInd, mins, deltas)
edgeInd=gce.edgeBins;
end
%   AUTHORSHIP
%   Primary Developer: <PERSON> <s<PERSON><PERSON><PERSON>@stanford.edu> 
%   Math Lead & Secondary Developer:  <PERSON> <<EMAIL>>
%   Bioinformatics Lead:  <PERSON> <wmoor<PERSON>@stanford.edu>
%   Provided by the Herzenberg Lab at Stanford University 
%   License: BSD 3 clause
%
function [out, N]=sortStructs(in, field, direction)
out=in;
N=length(in);
if N>0
    value=out(1).(field);
    if isnumeric(value)
        S=[out(:).(field)];
        [~,si]=sort(S, direction);
    elseif ischar(value)
        S={out(:).(field)};
        [~,si]=sort(S);
        if isequal(direction, 'descend')
            si=flip(si);
        end
    end
    out=out(si);
end
end

function M=remove_sparse(M, op)
%REMOVE_SPARSE Set to zero the entries of M for which op(M) = 1.
%
% M = REMOVE_SPARSE(M, op)
%
% Parameters
% ----------
% M: sparse matrix of size (m1, m2)
% 
% op: a function accepting doubles as input and outputting a boolean.
% 
% Returns
% -------
% M: sparse matrix of size (m1, m2)
%     The result of setting to zero the entries of M for which op(M) = 1.
%
%   AUTHORSHIP
%   Primary Developer: <PERSON> <<EMAIL>>
%   Math Lead & Secondary Developer:  <PERSON> <<EMAIL>>
%   Bioinformatics Lead:  <PERSON> <<EMAIL>>
%   Provided by the <PERSON>zenberg Lab at Stanford University 
%   License: BSD 3 clause
%

idxs=find(M);
logicalIdxs=feval(op, M(idxs));
removeIdxs=idxs(logicalIdxs);
M(removeIdxs)=0;
end
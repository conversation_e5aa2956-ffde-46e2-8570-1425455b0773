
%   AUTHORSHIP
%   Primary Developer: <PERSON> <sw<PERSON><PERSON>@stanford.edu> 
%   Math Lead & Secondary Developer:  <PERSON> <<EMAIL>>
%   Bioinformatics Lead:  <PERSON> <wmoor<PERSON>@stanford.edu>
%   Provided by the Herzenberg Lab at Stanford University 
%   License: BSD 3 clause
%
function [jd,pane]=msgWarning(txt, pause, where, title)
if nargin<4
    title='Warning...';
    if nargin<3
        where='center';
        if nargin<2
            pause=9;
        end
    end
end
[jd, pane]=msg(txt, pause, where, title, 'warning.png');
try
    if isstruct(txt)
        warning(txt.msg);
    else
        warning(txt);
    end
catch ex
    ex.getReport
end
end

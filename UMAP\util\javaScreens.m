%   AUTHORSHIP
%   Primary Developer: <PERSON> <sw<PERSON><PERSON>@stanford.edu> 
%   Math Lead & Secondary Developer:  <PERSON> <<EMAIL>>
%   Bioinformatics Lead:  <PERSON> <wmoor<PERSON>@stanford.edu>
%   Provided by the Herzenberg Lab at Stanford University 
%   License: BSD 3 clause
%

function screens=javaScreens
screens={};
ge=java.awt.GraphicsEnvironment.getLocalGraphicsEnvironment;
physicalScreens=ge.getScreenDevices;
N=length(physicalScreens);
for i=1:N
    pe=physicalScreens(i).getDefaultConfiguration.getBounds;
    screens{end+1}=pe;
end
end
